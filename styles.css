/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header h1 i {
    color: #007bff;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    gap: 2rem;
    padding: 2rem;
}

/* Sidebar */
.sidebar {
    width: 320px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    height: fit-content;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: calc(100vh - 140px);
}

.control-section {
    margin-bottom: 2rem;
}

.control-section:last-child {
    margin-bottom: 0;
}

.control-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f0f0f0;
}

.control-section h3 i {
    color: #007bff;
}

.control-group {
    margin-bottom: 1rem;
}

.control-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #555;
    font-size: 0.9rem;
}

.control-group input,
.control-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: white;
}

.control-group input:focus,
.control-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.control-row {
    display: flex;
    gap: 1rem;
}

.control-row .control-group {
    flex: 1;
}

/* Range Inputs */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #007bff;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
}

/* Color Inputs */
input[type="color"] {
    width: 100%;
    height: 45px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

input[type="color"]:hover {
    border-color: #007bff;
}

/* Shape and Icon Grids */
.shape-grid,
.icon-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.shape-btn,
.icon-btn {
    padding: 1rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #6c757d;
}

.shape-btn:hover,
.icon-btn:hover,
.shape-btn.active,
.icon-btn.active {
    border-color: #007bff;
    color: #007bff;
    background: rgba(0, 123, 255, 0.05);
    transform: translateY(-2px);
}

/* Position Grid */
.position-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.position-btn {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: #6c757d;
}

.position-btn:hover,
.position-btn.active {
    border-color: #007bff;
    color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

/* Radio Groups */
.radio-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 400;
    margin-bottom: 0;
}

.radio-group input[type="radio"] {
    width: auto;
    margin: 0;
}

/* Canvas Area */
.canvas-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.canvas-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
    margin-bottom: 1rem;
}

#logoCanvas {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: white;
}

.canvas-tools {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 0.75rem;
    border-radius: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.tool-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.tool-btn:hover {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    transform: scale(1.1);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Value Display */
#fontSizeValue,
#shapeSizeValue {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.download-options {
    margin-bottom: 1.5rem;
}

.download-option {
    margin-bottom: 1.5rem;
}

.download-option:last-child {
    margin-bottom: 0;
}

.download-option h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #333;
}

.download-option select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    background: white;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        flex-direction: column;
        padding: 1rem;
    }

    .sidebar {
        width: 100%;
        max-height: none;
    }

    .control-row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .shape-grid,
    .icon-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .canvas-container {
        padding: 1rem;
    }

    #logoCanvas {
        max-width: 100%;
        height: auto;
    }

    .shape-grid,
    .icon-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .position-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.25rem;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.2rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .sidebar {
        padding: 1rem;
    }

    .control-section h3 {
        font-size: 1rem;
    }
}
