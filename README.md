# Logo Generator

A comprehensive web-based logo generator built with HTML, CSS, and JavaScript. Create professional logos with text, shapes, icons, and various customization options.

## Features

### 🎨 Text Customization
- Company name and tagline input
- Multiple font families (Inter, Poppins, Roboto, Montserrat, Arial, Helvetica)
- Adjustable font size (20px - 80px)
- Color picker for text
- Real-time text preview

### 🔷 Shapes & Icons
- **Basic Shapes**: Circle, Square, Triangle, Diamond, Star, Heart
- **Business Icons**: Briefcase, Lightbulb, Rocket, Leaf, Gear, Globe
- Adjustable shape/icon size and color
- Easy selection with visual buttons

### 🎭 Background Options
- **Solid Colors**: Choose any solid background color
- **Gradients**: Create beautiful gradient backgrounds with two colors
- **Transparent**: No background for versatile use
- Real-time background preview

### 📐 Layout & Design
- **Canvas Sizes**: 
  - Square (400x400)
  - Landscape (500x300)
  - Portrait (300x500)
  - Banner (600x200)
- **Text Positioning**: 9-point grid positioning system
- **Alignment Tools**: Center elements with one click

### 💾 Export & Download
- **PNG Export**: High-quality raster images
- **SVG Export**: Scalable vector graphics
- **Multiple Resolutions**: 1x, 2x, 3x scaling options
- **Instant Download**: One-click download functionality

### 🔧 User Experience
- **Undo/Redo**: Full history management
- **Reset Function**: Quick reset to default settings
- **Real-time Preview**: See changes instantly
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern UI**: Clean, intuitive interface

## How to Use

1. **Open the App**: Open `index.html` in your web browser
2. **Enter Text**: Add your company name and optional tagline
3. **Choose Style**: Select font family, size, and color
4. **Add Graphics**: Pick a shape or icon and customize its appearance
5. **Set Background**: Choose solid color, gradient, or transparent
6. **Adjust Layout**: Select canvas size and position elements
7. **Download**: Click download and choose your preferred format and resolution

## File Structure

```
LogoGenerator/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling
├── script.js           # JavaScript functionality
└── README.md          # Documentation
```

## Technical Details

### Dependencies
- **Font Awesome 6**: For icons and UI elements
- **Google Fonts**: For typography options
- **HTML5 Canvas**: For logo rendering and export

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Features Implementation
- **Canvas API**: For drawing and rendering logos
- **File API**: For downloading generated logos
- **CSS Grid/Flexbox**: For responsive layout
- **ES6 Classes**: For organized JavaScript code
- **Local Storage**: For saving user preferences (future enhancement)

## Customization

### Adding New Fonts
Add new font imports to the HTML head and update the font family select options:

```html
<link href="https://fonts.googleapis.com/css2?family=YourFont:wght@300;400;500;600;700&display=swap" rel="stylesheet">
```

### Adding New Shapes
Extend the `drawShape()` method in `script.js` with new shape cases:

```javascript
case 'newShape':
    // Your shape drawing code here
    break;
```

### Adding New Icons
Update the icon mapping in the `drawIcon()` method:

```javascript
const iconMap = {
    'newIcon': '\uf123', // Font Awesome unicode
    // ... existing icons
};
```

## Future Enhancements

- [ ] Layer management system
- [ ] Custom shape upload
- [ ] Logo templates library
- [ ] Color palette suggestions
- [ ] Brand guideline export
- [ ] Social media size presets
- [ ] Advanced text effects
- [ ] Logo animation options
- [ ] Cloud save functionality
- [ ] Collaboration features

## License

This project is open source and available under the [MIT License](LICENSE).

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Support

If you encounter any issues or have questions, please open an issue on the project repository.

---

**Created with ❤️ for designers and entrepreneurs who need quick, professional logos.**
