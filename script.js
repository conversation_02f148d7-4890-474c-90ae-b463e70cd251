// Logo Generator App
class LogoGenerator {
    constructor() {
        this.canvas = document.getElementById('logoCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.history = [];
        this.historyStep = -1;
        this.currentShape = null;
        this.currentIcon = null;
        this.textPosition = 'center'; // Default text position

        this.initializeElements();
        this.bindEvents();
        this.saveState();
        this.updateCanvas();
    }

    initializeElements() {
        // Get all control elements
        this.elements = {
            companyName: document.getElementById('companyName'),
            tagline: document.getElementById('tagline'),
            fontFamily: document.getElementById('fontFamily'),
            fontSize: document.getElementById('fontSize'),
            fontSizeValue: document.getElementById('fontSizeValue'),
            textColor: document.getElementById('textColor'),
            shapeColor: document.getElementById('shapeColor'),
            shapeSize: document.getElementById('shapeSize'),
            shapeSizeValue: document.getElementById('shapeSizeValue'),
            bgColor: document.getElementById('bgColor'),
            gradientColor1: document.getElementById('gradientColor1'),
            gradientColor2: document.getElementById('gradientColor2'),
            canvasSize: document.getElementById('canvasSize'),
            resetBtn: document.getElementById('resetBtn'),
            downloadBtn: document.getElementById('downloadBtn'),
            undoBtn: document.getElementById('undoBtn'),
            redoBtn: document.getElementById('redoBtn'),
            centerBtn: document.getElementById('centerBtn'),
            downloadModal: document.getElementById('downloadModal'),
            closeModal: document.getElementById('closeModal'),
            confirmDownload: document.getElementById('confirmDownload')
        };

        // Get button groups
        this.shapeButtons = document.querySelectorAll('.shape-btn');
        this.iconButtons = document.querySelectorAll('.icon-btn');
        this.positionButtons = document.querySelectorAll('.position-btn');
        this.bgTypeRadios = document.querySelectorAll('input[name="bgType"]');
    }

    bindEvents() {
        // Text controls
        this.elements.companyName.addEventListener('input', () => this.updateCanvas());
        this.elements.tagline.addEventListener('input', () => this.updateCanvas());
        this.elements.fontFamily.addEventListener('change', () => this.updateCanvas());
        this.elements.fontSize.addEventListener('input', () => {
            this.elements.fontSizeValue.textContent = this.elements.fontSize.value + 'px';
            this.updateCanvas();
        });
        this.elements.textColor.addEventListener('change', () => this.updateCanvas());

        // Shape controls
        this.elements.shapeColor.addEventListener('change', () => this.updateCanvas());
        this.elements.shapeSize.addEventListener('input', () => {
            this.elements.shapeSizeValue.textContent = this.elements.shapeSize.value + 'px';
            this.updateCanvas();
        });

        // Background controls
        this.elements.bgColor.addEventListener('change', () => this.updateCanvas());
        this.elements.gradientColor1.addEventListener('change', () => this.updateCanvas());
        this.elements.gradientColor2.addEventListener('change', () => this.updateCanvas());

        // Canvas size
        this.elements.canvasSize.addEventListener('change', () => this.updateCanvasSize());

        // Shape buttons
        this.shapeButtons.forEach(btn => {
            btn.addEventListener('click', () => this.selectShape(btn));
        });

        // Icon buttons
        this.iconButtons.forEach(btn => {
            btn.addEventListener('click', () => this.selectIcon(btn));
        });

        // Position buttons
        this.positionButtons.forEach(btn => {
            btn.addEventListener('click', () => this.selectPosition(btn));
        });

        // Background type radios
        this.bgTypeRadios.forEach(radio => {
            radio.addEventListener('change', () => this.updateBackgroundControls());
        });

        // Action buttons
        this.elements.resetBtn.addEventListener('click', () => this.resetCanvas());
        this.elements.downloadBtn.addEventListener('click', () => this.showDownloadModal());
        this.elements.undoBtn.addEventListener('click', () => this.undo());
        this.elements.redoBtn.addEventListener('click', () => this.redo());
        this.elements.centerBtn.addEventListener('click', () => this.centerElements());

        // Modal events
        this.elements.closeModal.addEventListener('click', () => this.hideDownloadModal());
        this.elements.confirmDownload.addEventListener('click', () => this.downloadLogo());
        
        // Close modal on backdrop click
        this.elements.downloadModal.addEventListener('click', (e) => {
            if (e.target === this.elements.downloadModal) {
                this.hideDownloadModal();
            }
        });
    }

    updateCanvas() {
        this.clearCanvas();
        this.drawBackground();
        this.drawShape();
        this.drawIcon();
        this.drawText();
        this.saveState();
    }

    clearCanvas() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    drawBackground() {
        const bgType = document.querySelector('input[name="bgType"]:checked').value;
        
        if (bgType === 'solid') {
            this.ctx.fillStyle = this.elements.bgColor.value;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        } else if (bgType === 'gradient') {
            const gradient = this.ctx.createLinearGradient(0, 0, this.canvas.width, this.canvas.height);
            gradient.addColorStop(0, this.elements.gradientColor1.value);
            gradient.addColorStop(1, this.elements.gradientColor2.value);
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
        // Transparent background doesn't need drawing
    }

    drawShape() {
        if (!this.currentShape) return;

        const size = parseInt(this.elements.shapeSize.value);
        const shapePosition = this.getShapePosition();
        const centerX = shapePosition.x;
        const centerY = shapePosition.y;

        this.ctx.fillStyle = this.elements.shapeColor.value;
        this.ctx.strokeStyle = this.elements.shapeColor.value;
        this.ctx.lineWidth = 2;

        switch (this.currentShape) {
            case 'circle':
                this.ctx.beginPath();
                this.ctx.arc(centerX, centerY, size / 2, 0, 2 * Math.PI);
                this.ctx.fill();
                break;
            case 'square':
                this.ctx.fillRect(centerX - size / 2, centerY - size / 2, size, size);
                break;
            case 'triangle':
                this.ctx.beginPath();
                this.ctx.moveTo(centerX, centerY - size / 2);
                this.ctx.lineTo(centerX - size / 2, centerY + size / 2);
                this.ctx.lineTo(centerX + size / 2, centerY + size / 2);
                this.ctx.closePath();
                this.ctx.fill();
                break;
            case 'diamond':
                this.ctx.beginPath();
                this.ctx.moveTo(centerX, centerY - size / 2);
                this.ctx.lineTo(centerX + size / 2, centerY);
                this.ctx.lineTo(centerX, centerY + size / 2);
                this.ctx.lineTo(centerX - size / 2, centerY);
                this.ctx.closePath();
                this.ctx.fill();
                break;
            case 'star':
                this.drawStar(centerX, centerY, 5, size / 2, size / 4);
                break;
            case 'heart':
                this.drawHeart(centerX, centerY, size / 2);
                break;
        }
    }

    drawStar(cx, cy, spikes, outerRadius, innerRadius) {
        let rot = Math.PI / 2 * 3;
        let x = cx;
        let y = cy;
        const step = Math.PI / spikes;

        this.ctx.beginPath();
        this.ctx.moveTo(cx, cy - outerRadius);
        
        for (let i = 0; i < spikes; i++) {
            x = cx + Math.cos(rot) * outerRadius;
            y = cy + Math.sin(rot) * outerRadius;
            this.ctx.lineTo(x, y);
            rot += step;

            x = cx + Math.cos(rot) * innerRadius;
            y = cy + Math.sin(rot) * innerRadius;
            this.ctx.lineTo(x, y);
            rot += step;
        }
        
        this.ctx.lineTo(cx, cy - outerRadius);
        this.ctx.closePath();
        this.ctx.fill();
    }

    drawHeart(cx, cy, size) {
        this.ctx.beginPath();
        this.ctx.moveTo(cx, cy + size / 4);
        this.ctx.bezierCurveTo(cx, cy, cx - size / 2, cy - size / 2, cx - size / 2, cy - size / 4);
        this.ctx.bezierCurveTo(cx - size / 2, cy - size / 8, cx - size / 4, cy, cx, cy + size / 4);
        this.ctx.bezierCurveTo(cx + size / 4, cy, cx + size / 2, cy - size / 8, cx + size / 2, cy - size / 4);
        this.ctx.bezierCurveTo(cx + size / 2, cy - size / 2, cx, cy, cx, cy + size / 4);
        this.ctx.fill();
    }

    drawIcon() {
        if (!this.currentIcon) return;

        const size = parseInt(this.elements.shapeSize.value);
        const shapePosition = this.getShapePosition();
        const centerX = shapePosition.x;
        const centerY = shapePosition.y;

        this.ctx.fillStyle = this.elements.shapeColor.value;
        this.ctx.font = `${size}px "Font Awesome 6 Free"`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        const iconMap = {
            'briefcase': '\uf0b1',
            'lightbulb': '\uf0eb',
            'rocket': '\uf135',
            'leaf': '\uf06c',
            'cog': '\uf013',
            'globe': '\uf0ac'
        };

        if (iconMap[this.currentIcon]) {
            this.ctx.fillText(iconMap[this.currentIcon], centerX, centerY);
        }
    }

    drawText() {
        const companyName = this.elements.companyName.value;
        const tagline = this.elements.tagline.value;
        const fontSize = parseInt(this.elements.fontSize.value);
        const fontFamily = this.elements.fontFamily.value;
        const textColor = this.elements.textColor.value;

        if (!companyName && !tagline) return;

        this.ctx.fillStyle = textColor;
        this.ctx.font = `bold ${fontSize}px ${fontFamily}`;
        this.ctx.textBaseline = 'middle';

        // Calculate text positions based on selected position
        const positions = this.getTextPositions();

        // Draw company name
        if (companyName) {
            this.ctx.textAlign = positions.align;
            this.ctx.fillText(companyName, positions.x, positions.y);
        }

        // Draw tagline
        if (tagline) {
            this.ctx.font = `${Math.floor(fontSize * 0.6)}px ${fontFamily}`;
            this.ctx.textAlign = positions.align;
            const taglineOffset = companyName ? fontSize * 0.8 : 0;
            this.ctx.fillText(tagline, positions.x, positions.y + taglineOffset);
        }
    }

    getTextPositions() {
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;
        const margin = 40; // Margin from edges
        const shapeOffset = (this.currentShape || this.currentIcon) ? 80 : 0;

        const positions = {
            'top-left': { x: margin, y: margin + 20, align: 'left' },
            'top-center': { x: canvasWidth / 2, y: margin + 20, align: 'center' },
            'top-right': { x: canvasWidth - margin, y: margin + 20, align: 'right' },
            'center-left': { x: margin, y: canvasHeight / 2 + shapeOffset, align: 'left' },
            'center': { x: canvasWidth / 2, y: canvasHeight / 2 + shapeOffset, align: 'center' },
            'center-right': { x: canvasWidth - margin, y: canvasHeight / 2 + shapeOffset, align: 'right' },
            'bottom-left': { x: margin, y: canvasHeight - margin - 20, align: 'left' },
            'bottom-center': { x: canvasWidth / 2, y: canvasHeight - margin - 20, align: 'center' },
            'bottom-right': { x: canvasWidth - margin, y: canvasHeight - margin - 20, align: 'right' }
        };

        return positions[this.textPosition] || positions['center'];
    }

    getShapePosition() {
        const canvasWidth = this.canvas.width;
        const canvasHeight = this.canvas.height;

        // Position shapes to complement text position
        const shapePositions = {
            'top-left': { x: canvasWidth / 2, y: canvasHeight / 2 + 40 },
            'top-center': { x: canvasWidth / 2, y: canvasHeight / 2 },
            'top-right': { x: canvasWidth / 2, y: canvasHeight / 2 + 40 },
            'center-left': { x: canvasWidth / 2 + 60, y: canvasHeight / 2 },
            'center': { x: canvasWidth / 2, y: canvasHeight / 2 - 40 },
            'center-right': { x: canvasWidth / 2 - 60, y: canvasHeight / 2 },
            'bottom-left': { x: canvasWidth / 2, y: canvasHeight / 2 - 40 },
            'bottom-center': { x: canvasWidth / 2, y: canvasHeight / 2 - 20 },
            'bottom-right': { x: canvasWidth / 2, y: canvasHeight / 2 - 40 }
        };

        return shapePositions[this.textPosition] || { x: canvasWidth / 2, y: canvasHeight / 2 - 40 };
    }

    selectShape(button) {
        this.shapeButtons.forEach(btn => btn.classList.remove('active'));
        this.iconButtons.forEach(btn => btn.classList.remove('active'));
        
        button.classList.add('active');
        this.currentShape = button.dataset.shape;
        this.currentIcon = null;
        this.updateCanvas();
    }

    selectIcon(button) {
        this.shapeButtons.forEach(btn => btn.classList.remove('active'));
        this.iconButtons.forEach(btn => btn.classList.remove('active'));
        
        button.classList.add('active');
        this.currentIcon = button.dataset.icon;
        this.currentShape = null;
        this.updateCanvas();
    }

    selectPosition(button) {
        this.positionButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        this.textPosition = button.dataset.position;
        this.updateCanvas();
    }

    updateBackgroundControls() {
        const bgType = document.querySelector('input[name="bgType"]:checked').value;
        const solidControls = document.getElementById('solidBgControls');
        const gradientControls = document.getElementById('gradientBgControls');

        solidControls.classList.toggle('hidden', bgType !== 'solid');
        gradientControls.classList.toggle('hidden', bgType !== 'gradient');
        
        this.updateCanvas();
    }

    updateCanvasSize() {
        const size = this.elements.canvasSize.value;
        const [width, height] = size.split('x').map(Number);
        
        this.canvas.width = width;
        this.canvas.height = height;
        this.updateCanvas();
    }

    resetCanvas() {
        // Reset all controls to default values
        this.elements.companyName.value = 'Your Logo';
        this.elements.tagline.value = '';
        this.elements.fontFamily.value = 'Inter';
        this.elements.fontSize.value = '48';
        this.elements.fontSizeValue.textContent = '48px';
        this.elements.textColor.value = '#333333';
        this.elements.shapeColor.value = '#007bff';
        this.elements.shapeSize.value = '60';
        this.elements.shapeSizeValue.textContent = '60px';
        this.elements.bgColor.value = '#ffffff';
        this.elements.canvasSize.value = '400x400';

        // Reset active states
        this.shapeButtons.forEach(btn => btn.classList.remove('active'));
        this.iconButtons.forEach(btn => btn.classList.remove('active'));
        this.positionButtons.forEach(btn => btn.classList.remove('active'));
        this.positionButtons[4].classList.add('active'); // Center position

        // Reset background type
        document.querySelector('input[name="bgType"][value="solid"]').checked = true;
        this.updateBackgroundControls();

        this.currentShape = null;
        this.currentIcon = null;
        this.textPosition = 'center'; // Reset text position
        this.updateCanvasSize();
    }

    saveState() {
        this.historyStep++;
        if (this.historyStep < this.history.length) {
            this.history.length = this.historyStep;
        }
        this.history.push(this.canvas.toDataURL());
    }

    undo() {
        if (this.historyStep > 0) {
            this.historyStep--;
            this.restoreState();
        }
    }

    redo() {
        if (this.historyStep < this.history.length - 1) {
            this.historyStep++;
            this.restoreState();
        }
    }

    restoreState() {
        const img = new Image();
        img.onload = () => {
            this.clearCanvas();
            this.ctx.drawImage(img, 0, 0);
        };
        img.src = this.history[this.historyStep];
    }

    centerElements() {
        // This would center all elements - for now just redraw
        this.updateCanvas();
    }

    showDownloadModal() {
        this.elements.downloadModal.classList.remove('hidden');
        this.elements.downloadModal.classList.add('fade-in');
    }

    hideDownloadModal() {
        this.elements.downloadModal.classList.add('hidden');
        this.elements.downloadModal.classList.remove('fade-in');
    }

    downloadLogo() {
        const format = document.querySelector('input[name="format"]:checked').value;
        const scale = parseInt(document.getElementById('downloadSize').value);
        
        if (format === 'png') {
            this.downloadPNG(scale);
        } else if (format === 'svg') {
            this.downloadSVG();
        }
        
        this.hideDownloadModal();
    }

    downloadPNG(scale = 1) {
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        
        tempCanvas.width = this.canvas.width * scale;
        tempCanvas.height = this.canvas.height * scale;
        
        tempCtx.scale(scale, scale);
        tempCtx.drawImage(this.canvas, 0, 0);
        
        const link = document.createElement('a');
        link.download = `logo_${Date.now()}.png`;
        link.href = tempCanvas.toDataURL('image/png');
        link.click();
    }

    downloadSVG() {
        // Basic SVG export - would need more complex implementation for full features
        const svgData = this.generateSVG();
        const blob = new Blob([svgData], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.download = `logo_${Date.now()}.svg`;
        link.href = url;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    generateSVG() {
        // This is a simplified SVG generation - would need full implementation
        const companyName = this.elements.companyName.value;
        const fontSize = this.elements.fontSize.value;
        const textColor = this.elements.textColor.value;
        
        return `
            <svg width="${this.canvas.width}" height="${this.canvas.height}" xmlns="http://www.w3.org/2000/svg">
                <text x="${this.canvas.width/2}" y="${this.canvas.height/2}" 
                      text-anchor="middle" dominant-baseline="middle" 
                      font-size="${fontSize}" fill="${textColor}" 
                      font-family="${this.elements.fontFamily.value}">
                    ${companyName}
                </text>
            </svg>
        `;
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LogoGenerator();
});
