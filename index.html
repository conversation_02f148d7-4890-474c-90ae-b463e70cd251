<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo Generator - Create Professional Logos</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-palette"></i> Logo Generator</h1>
                <div class="header-actions">
                    <button class="btn btn-secondary" id="resetBtn">
                        <i class="fas fa-refresh"></i> Reset
                    </button>
                    <button class="btn btn-primary" id="downloadBtn">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- Sidebar Controls -->
            <aside class="sidebar">
                <div class="control-section">
                    <h3><i class="fas fa-font"></i> Text</h3>
                    <div class="control-group">
                        <label for="companyName">Company Name</label>
                        <input type="text" id="companyName" placeholder="Enter company name" value="Your Logo">
                    </div>
                    <div class="control-group">
                        <label for="tagline">Tagline</label>
                        <input type="text" id="tagline" placeholder="Enter tagline (optional)">
                    </div>
                    <div class="control-group">
                        <label for="fontFamily">Font Family</label>
                        <select id="fontFamily">
                            <option value="Inter">Inter</option>
                            <option value="Poppins">Poppins</option>
                            <option value="Roboto">Roboto</option>
                            <option value="Montserrat">Montserrat</option>
                            <option value="Arial">Arial</option>
                            <option value="Helvetica">Helvetica</option>
                        </select>
                    </div>
                    <div class="control-row">
                        <div class="control-group">
                            <label for="fontSize">Font Size</label>
                            <input type="range" id="fontSize" min="20" max="80" value="48">
                            <span id="fontSizeValue">48px</span>
                        </div>
                        <div class="control-group">
                            <label for="textColor">Text Color</label>
                            <input type="color" id="textColor" value="#333333">
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3><i class="fas fa-shapes"></i> Shapes & Icons</h3>
                    <div class="shape-grid">
                        <button class="shape-btn" data-shape="circle"><i class="fas fa-circle"></i></button>
                        <button class="shape-btn" data-shape="square"><i class="fas fa-square"></i></button>
                        <button class="shape-btn" data-shape="triangle"><i class="fas fa-play"></i></button>
                        <button class="shape-btn" data-shape="diamond"><i class="fas fa-diamond"></i></button>
                        <button class="shape-btn" data-shape="star"><i class="fas fa-star"></i></button>
                        <button class="shape-btn" data-shape="heart"><i class="fas fa-heart"></i></button>
                    </div>
                    <div class="icon-grid">
                        <button class="icon-btn" data-icon="briefcase"><i class="fas fa-briefcase"></i></button>
                        <button class="icon-btn" data-icon="lightbulb"><i class="fas fa-lightbulb"></i></button>
                        <button class="icon-btn" data-icon="rocket"><i class="fas fa-rocket"></i></button>
                        <button class="icon-btn" data-icon="leaf"><i class="fas fa-leaf"></i></button>
                        <button class="icon-btn" data-icon="cog"><i class="fas fa-cog"></i></button>
                        <button class="icon-btn" data-icon="globe"><i class="fas fa-globe"></i></button>
                    </div>
                    <div class="control-row">
                        <div class="control-group">
                            <label for="shapeColor">Shape Color</label>
                            <input type="color" id="shapeColor" value="#007bff">
                        </div>
                        <div class="control-group">
                            <label for="shapeSize">Shape Size</label>
                            <input type="range" id="shapeSize" min="20" max="100" value="60">
                            <span id="shapeSizeValue">60px</span>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3><i class="fas fa-fill-drip"></i> Background</h3>
                    <div class="control-group">
                        <label>Background Type</label>
                        <div class="radio-group">
                            <label><input type="radio" name="bgType" value="solid" checked> Solid</label>
                            <label><input type="radio" name="bgType" value="gradient"> Gradient</label>
                            <label><input type="radio" name="bgType" value="transparent"> Transparent</label>
                        </div>
                    </div>
                    <div class="control-row" id="solidBgControls">
                        <div class="control-group">
                            <label for="bgColor">Background Color</label>
                            <input type="color" id="bgColor" value="#ffffff">
                        </div>
                    </div>
                    <div class="control-row hidden" id="gradientBgControls">
                        <div class="control-group">
                            <label for="gradientColor1">Color 1</label>
                            <input type="color" id="gradientColor1" value="#007bff">
                        </div>
                        <div class="control-group">
                            <label for="gradientColor2">Color 2</label>
                            <input type="color" id="gradientColor2" value="#00d4ff">
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3><i class="fas fa-expand-arrows-alt"></i> Layout</h3>
                    <div class="control-group">
                        <label>Canvas Size</label>
                        <select id="canvasSize">
                            <option value="400x400">Square (400x400)</option>
                            <option value="500x300">Landscape (500x300)</option>
                            <option value="300x500">Portrait (300x500)</option>
                            <option value="600x200">Banner (600x200)</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>Text Position</label>
                        <div class="position-grid">
                            <button class="position-btn" data-position="top-left"><i class="fas fa-arrow-up"></i><i class="fas fa-arrow-left"></i></button>
                            <button class="position-btn" data-position="top-center"><i class="fas fa-arrow-up"></i></button>
                            <button class="position-btn" data-position="top-right"><i class="fas fa-arrow-up"></i><i class="fas fa-arrow-right"></i></button>
                            <button class="position-btn" data-position="center-left"><i class="fas fa-arrow-left"></i></button>
                            <button class="position-btn active" data-position="center"><i class="fas fa-dot-circle"></i></button>
                            <button class="position-btn" data-position="center-right"><i class="fas fa-arrow-right"></i></button>
                            <button class="position-btn" data-position="bottom-left"><i class="fas fa-arrow-down"></i><i class="fas fa-arrow-left"></i></button>
                            <button class="position-btn" data-position="bottom-center"><i class="fas fa-arrow-down"></i></button>
                            <button class="position-btn" data-position="bottom-right"><i class="fas fa-arrow-down"></i><i class="fas fa-arrow-right"></i></button>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Canvas Area -->
            <main class="canvas-area">
                <div class="canvas-container">
                    <canvas id="logoCanvas" width="400" height="400"></canvas>
                </div>
                <div class="canvas-tools">
                    <button class="tool-btn" id="undoBtn" title="Undo">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="tool-btn" id="redoBtn" title="Redo">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button class="tool-btn" id="centerBtn" title="Center Elements">
                        <i class="fas fa-crosshairs"></i>
                    </button>
                </div>
            </main>
        </div>

        <!-- Download Modal -->
        <div class="modal hidden" id="downloadModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Download Logo</h3>
                    <button class="close-btn" id="closeModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="download-options">
                        <div class="download-option">
                            <h4>Format</h4>
                            <div class="radio-group">
                                <label><input type="radio" name="format" value="png" checked> PNG</label>
                                <label><input type="radio" name="format" value="svg"> SVG</label>
                            </div>
                        </div>
                        <div class="download-option">
                            <h4>Size</h4>
                            <select id="downloadSize">
                                <option value="1">Original Size</option>
                                <option value="2">2x (High Resolution)</option>
                                <option value="3">3x (Ultra High Resolution)</option>
                            </select>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-full" id="confirmDownload">
                        <i class="fas fa-download"></i> Download Logo
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
